package com.project.management.notes.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.notes.mappers.NotesMapper
import com.project.management.notes.models.CreateNoteRequest
import com.project.management.notes.Note
import com.project.management.notes.models.PatchNoteRequest
import com.project.management.notes.repositories.NotesRepository
import com.project.management.notes.validators.NotesValidator
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class NotesService(
    private val notesRepository: NotesRepository,
    private val currentUser: CurrentUserConfig,
    private val validator: NotesValidator,
    private val mapper: NotesMapper,
) {
    fun getByBeneficiaryId(beneficiaryId: Long): List<Note> {
        val user = currentUser.getCurrentUser()
        return notesRepository.findAllByBeneficiaryIdAndOrganizationId(
            beneficiaryId = beneficiaryId,
            organizationId = user.organizationId
        )
    }

    fun getByCustomerId(customerId: Long): List<Note> {
        val user = currentUser.getCurrentUser()
        return notesRepository.findAllByCustomerIdAndOrganizationId(
            customerId = customerId,
            organizationId = user.organizationId
        )
    }

    fun getByProjectId(projectId: Long): List<Note> {
        val user = currentUser.getCurrentUser()
        return notesRepository.findAllByProjectIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )
    }

    fun getById(noteId: Long): Note {
        val loggedInUser = currentUser.getCurrentUser()
        return validator.noteExists(noteId = noteId, organizationId = loggedInUser.organizationId)
    }

    fun create(request: CreateNoteRequest): Note {
        val loggedInUser = currentUser.getCurrentUser()
        val validatedRequest = validator.validCreateNoteRequest(request)
        val note = mapper.toNoteEntity(
            request = validatedRequest,
            organizationId = loggedInUser.organizationId,
            userId = loggedInUser.id,
            createdBy = loggedInUser,
        )
        return notesRepository.save(note)
    }

    fun edit(request: PatchNoteRequest, noteId: Long): Note {
        val loggedInUser = currentUser.getCurrentUser()
        val note = validator.noteExists(
            noteId = noteId,
            organizationId = loggedInUser.organizationId
        )
        val validatedRequest = validator.validPatchNoteRequest(request)
        if (note.version != validatedRequest.version) throw BusinessException.ConflictException()
        note.note = validatedRequest.note ?: note.note
        note.beneficiaryId = validatedRequest.beneficiaryId ?: note.beneficiaryId
        note.customerId = validatedRequest.customerId ?: note.customerId
        note.projectId = validatedRequest.projectId ?: note.projectId
        note.updatedBy = loggedInUser.id!!

        return notesRepository.save(note)
    }

    @Transactional
    fun delete(noteId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val note = validator.noteExists(
            noteId = noteId,
            organizationId = loggedInUser.organizationId
        )
        notesRepository.deleteByIdAndOrganizationId(
            id = noteId,
            organizationId = loggedInUser.organizationId,
            updatedBy = loggedInUser.id!!
        )
    }
}