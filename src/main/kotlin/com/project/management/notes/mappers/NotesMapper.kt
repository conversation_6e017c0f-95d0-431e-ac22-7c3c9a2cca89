package com.project.management.notes.mappers

import com.project.management.common.annotation.MappingOrganizationEntity
import com.project.management.users.User
import com.project.management.notes.models.CreateNoteRequest
import com.project.management.notes.Note
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface NotesMapper {

    @MappingOrganizationEntity
    fun toNoteEntity(
        request: CreateNoteRequest,
        organizationId: Long,
        createdBy: User,
        userId: Long?,
    ): Note
}