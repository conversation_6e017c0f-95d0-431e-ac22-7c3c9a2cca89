package com.project.management.notes

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class NotePostRequest(
    val note: String,
    val beneficiaryId: Long?,
    val customerId: Long?,
    val projectId: Long?,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class NotePatchRequest(
    val note: String?,
    val beneficiaryId: Long?,
    val customerId: Long?,
    val projectId: Long?,
    val version: Long
)
