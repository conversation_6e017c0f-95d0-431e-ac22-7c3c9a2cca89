package com.project.management.notes.repositories

import com.project.management.notes.Note
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface NotesRepository : JpaRepository<Note, Long> {
    fun findAllByOrganizationId(organizationId: Long): List<Note>

    fun findAllByBeneficiaryIdAndOrganizationId(
        beneficiaryId: Long,
        organizationId: Long
    ): List<Note>

    fun findAllByCustomerIdAndOrganizationId(
        customerId: Long,
        organizationId: Long
    ): List<Note>

    fun findAllByProjectIdAndOrganizationId(projectId: Long, organizationId: Long): List<Note>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): Note?

    @Modifying
    @Query(
        value = "UPDATE notes SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true,
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}