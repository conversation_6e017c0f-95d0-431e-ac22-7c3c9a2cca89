package com.project.management.notes.controllers

import com.project.management.notes.models.CreateNoteRequest
import com.project.management.notes.Note
import com.project.management.notes.models.PatchNoteRequest
import com.project.management.notes.services.NotesService
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/notes")
class NotesController(
    private val notesService: NotesService
) {

    @GetMapping("/beneficiaries/{beneficiaryId}")
    fun getByBeneficiaryId(
        @PathVariable beneficiaryId: Long
    ): ResponseEntity<List<Note>> {
        return ResponseEntity.ok(notesService.getByBeneficiaryId(beneficiaryId = beneficiaryId))
    }

    @GetMapping("/customers/{customerId}")
    fun getByCustomerId(
        @PathVariable customerId: Long
    ): ResponseEntity<List<Note>> {
        return ResponseEntity.ok(notesService.getByCustomerId(customerId = customerId))
    }

    @GetMapping("/projects/{projectId}")
    fun getByProjectId(
        @PathVariable projectId: Long
    ): ResponseEntity<List<Note>> {
        return ResponseEntity.ok(notesService.getByProjectId(projectId = projectId))
    }

    @GetMapping("/{noteId}")
    fun getById(
        @PathVariable noteId: Long
    ): ResponseEntity<Note> {
        return ResponseEntity.ok(notesService.getById(noteId = noteId))
    }

    @PostMapping
    fun create(
        @RequestBody request: CreateNoteRequest
    ): ResponseEntity<Note> {
        return ResponseEntity.ok(notesService.create(request))
    }

    @PatchMapping("/{noteId}")
    fun edit(
        @PathVariable noteId: Long,
        @RequestBody request: PatchNoteRequest
    ): ResponseEntity<Note> {
        return ResponseEntity.ok(notesService.edit(request, noteId))
    }

    @DeleteMapping("/{noteId}")
    fun delete(
        @PathVariable noteId: Long
    ): ResponseEntity<Unit> {
        notesService.delete(noteId)
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build()
    }
}