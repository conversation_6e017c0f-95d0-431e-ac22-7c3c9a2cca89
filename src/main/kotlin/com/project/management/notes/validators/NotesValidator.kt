package com.project.management.notes.validators

import com.project.management.common.exceptions.BusinessException
import com.project.management.notes.models.CreateNoteRequest
import com.project.management.notes.Note
import com.project.management.notes.models.PatchNoteRequest
import com.project.management.notes.repositories.NotesRepository
import org.springframework.stereotype.Component

@Component
class NotesValidator(
    private val repository: NotesRepository
) {

    fun noteExists(noteId: Long, organizationId: Long): Note {
        return repository.findByIdAndOrganizationId(id = noteId, organizationId = organizationId)
            ?: throw BusinessException.NotFoundException("Note not found")
    }

    fun validCreateNoteRequest(request: CreateNoteRequest): CreateNoteRequest {
        if (request.beneficiaryId == null && request.customerId == null && request.projectId == null) {
            throw BusinessException
                .BadRequestException("At least one of beneficiaryId, customerId or projectId must be provided")
        }
        return request
    }

    fun validPatchNoteRequest(request: PatchNoteRequest): PatchNoteRequest {
        if (request.beneficiaryId == null && request.customerId == null && request.projectId == null) {
            throw BusinessException
                .BadRequestException("At least one of beneficiaryId, customerId or projectId must be provided")
        }
        return request
    }
}